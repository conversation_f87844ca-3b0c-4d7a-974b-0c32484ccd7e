<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Preview</title>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --accent: #4cc9f0;
            --dark: #1a1a2e;
            --light: #f8f9fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
            min-height: 100vh;
            color: var(--dark);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 4rem 1.5rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        .section-header h1 {
            font-size: 2.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .section-header p {
            color: #6c757d;
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
        }

        /* Swiper Customization */
        .swiper {
            width: 100%;
            padding: 3rem 0;
        }

        .swiper-slide {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            transform: scale(0.9);
            opacity: 0.5;
        }

        .swiper-slide-active {
            transform: scale(1);
            opacity: 1;
            z-index: 2;
        }

        .swiper-slide-next, .swiper-slide-prev {
            opacity: 0.8;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .gallery-img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            transition: transform 0.5s ease;
            cursor: pointer;
        }
        
        .gallery-item a {
            display: block;
            overflow: hidden;
        }

        .gallery-item:hover .gallery-img {
            transform: scale(1.05);
        }

        .gallery-caption {
            padding: 1.5rem;
            background: white;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .gallery-caption h3 {
            font-size: 1.3rem;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .gallery-caption p {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* Navigation Buttons */
        .swiper-button-next,
        .swiper-button-prev {
            background: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            color: var(--primary);
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .swiper-button-next:hover,
        .swiper-button-prev:hover {
            transform: scale(1.1);
            background: var(--primary);
            color: white;
        }

        /* Pagination */
        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: #d1d5db;
            opacity: 1;
            transition: all 0.3s ease;
        }

        .swiper-pagination-bullet-active {
            background: var(--primary);
            transform: scale(1.2);
            width: 30px;
            border-radius: 4px;
        }

        /* Animations */
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .section-header h1 {
                font-size: 2.2rem;
            }
            
            .gallery-img {
                height: 250px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 3rem 1rem;
            }
            
            .section-header h1 {
                font-size: 2rem;
            }
            
            .swiper-button-next,
            .swiper-button-prev {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="section-header">
            <h1>Our Platform Preview</h1>
            <p>Explore the key features of our platform </p>
        </header>

        <!-- Swiper -->
        <div class="swiper mySwiper">
            <div class="swiper-wrapper">
                <!-- Slide 1 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/employer-signin_html_2025-08-07_18-17-55_7953.webp" data-lightbox="platform-preview" data-title="Employer Sign In">
                            <img src="preivew/employer-signin_html_2025-08-07_18-17-55_7953.webp" alt="Employer Sign In" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Employer Sign In</h3>
                            <p>Secure and intuitive login interface for employers to access their dashboard and manage postings.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 2 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/employer-signup_html_2025-08-07_18-19-58_3305.webp" data-lightbox="platform-preview" data-title="Employer Sign Up">
                            <img src="preivew/employer-signup_html_2025-08-07_18-19-58_3305.webp" alt="Employer Sign Up" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Employer Sign Up</h3>
                            <p>Streamlined registration process for new employers to join our platform.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 3 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/job-flow-test_html_2025-08-07_18-21-01_3998.webp" data-lightbox="platform-preview" data-title="Job Application Process">
                            <img src="preivew/job-flow-test_html_2025-08-07_18-21-01_3998.webp" alt="Job Flow Test" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Job Application Process</h3>
                            <p>Experience our seamless job application workflow designed for efficiency and ease of use.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 4 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/student-profile_html_2025-08-07_18-20-40_4808.webp" data-lightbox="platform-preview" data-title="Student Profiles">
                            <img src="preivew/student-profile_html_2025-08-07_18-20-40_4808.webp" alt="Student Profile" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Student Profiles</h3>
                            <p>Comprehensive student profiles showcasing skills, experience, and qualifications.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 5 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/studentlogin_html_2025-08-07_18-18-33_4993.webp" data-lightbox="platform-preview" data-title="Student Login">
                            <img src="preivew/studentlogin_html_2025-08-07_18-18-33_4993.webp" alt="Student Login" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Student Login</h3>
                            <p>Secure access point for students to explore opportunities and manage applications.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 6 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_15-12-58.jpg" data-lightbox="platform-preview" data-title="Feature 1">
                            <img src="preivew/photo_2025-08-07_15-12-58.jpg" alt="Feature 1" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 1</h3>
                            <p>Explore the first of our platform's advanced features.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 7 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_15-12-58 (2).jpg" data-lightbox="platform-preview" data-title="Feature 2">
                            <img src="preivew/photo_2025-08-07_15-12-58 (2).jpg" alt="Feature 2" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 2</h3>
                            <p>Discover the second key feature of our platform.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 8 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_15-12-58 (3).jpg" data-lightbox="platform-preview" data-title="Feature 3">
                            <img src="preivew/photo_2025-08-07_15-12-58 (3).jpg" alt="Feature 3" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 3</h3>
                            <p>Experience the third innovative feature we offer.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 9 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_15-12-58 (4).jpg" data-lightbox="platform-preview" data-title="Feature 4">
                            <img src="preivew/photo_2025-08-07_15-12-58 (4).jpg" alt="Feature 4" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 4</h3>
                            <p>Check out this essential functionality in action.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 10 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_15-12-58 (5).jpg" data-lightbox="platform-preview" data-title="Feature 5">
                            <img src="preivew/photo_2025-08-07_15-12-58 (5).jpg" alt="Feature 5" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 5</h3>
                            <p>Another powerful tool at your fingertips.</p>
                        </div>
                    </div>
                </div>

                <!-- Slide 11 -->
                <div class="swiper-slide">
                    <div class="gallery-item">
                        <a href="preivew/photo_2025-08-07_21-35-22.jpg" data-lightbox="platform-preview" data-title="Feature 6">
                            <img src="preivew/photo_2025-08-07_21-35-22.jpg" alt="Feature 6" class="gallery-img">
                        </a>
                        <div class="gallery-caption">
                            <h3>Feature 6</h3>
                            <p>Final feature showcasing our platform's capabilities.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation buttons -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
            
            <!-- Pagination -->
            <div class="swiper-pagination"></div>
        </div>
    </div>

    <!-- Lightbox2 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
    
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <!-- Lightbox2 JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    
    <!-- Lightbox Configuration -->
    <script>
        // Configure Lightbox
        lightbox.option({
            'resizeDuration': 200,
            'wrapAround': true,
            'showImageNumberLabel': true,
            'alwaysShowNavOnTouchDevices': true,
            'fadeDuration': 300,
            'imageFadeDuration': 300,
            'disableScrolling': true
        });
    </script>
    
    <!-- Initialize Swiper -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const swiper = new Swiper(".mySwiper", {
                effect: "coverflow",
                grabCursor: true,
                centeredSlides: true,
                slidesPerView: 'auto',
                coverflowEffect: {
                    rotate: 20,
                    stretch: 0,
                    depth: 200,
                    modifier: 1,
                    slideShadows: true,
                },
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                breakpoints: {
                    320: {
                        slidesPerView: 1,
                        spaceBetween: 20
                    },
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 40
                    }
                }
            });

            // Add animation class to slides when they become active
            swiper.on('slideChange', function () {
                const activeSlide = document.querySelector('.swiper-slide-active .gallery-item');
                if (activeSlide) {
                    activeSlide.classList.add('animate__animated', 'animate__pulse');
                    setTimeout(() => {
                        activeSlide.classList.remove('animate__animated', 'animate__pulse');
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
