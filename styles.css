/* Base Styles */
:root {
    --color-primary: #d17e7e;
    --color-primary-dark: #c06c6c;
    --color-secondary: #5a3e5d;
    --color-secondary-light: #8c6c8e;
    --color-secondary-dark: #4a3249;
    --color-bg-light: #f8f0eb;
    --color-bg-light-alt: #f5e6e0;
    --color-white: #ffffff;
    --color-text: #333333;
    --color-text-light: #666666;
    --font-family: "Inter", sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Height of the fixed header */
  }
  
  body {
    font-family: var(--font-family);
    color: var(--color-text);
    line-height: 1.6;
    background-color: var(--color-white);
  }
  
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  h4 {
    font-size: 1.25rem;
  }
  
  p {
    margin-bottom: 1rem;
    color: var(--color-text-light);
  }
  
  a {
    color: var(--color-primary);
    text-decoration: none;
    transition: var(--transition);
  }
  
  a:hover {
    color: var(--color-primary-dark);
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  ul {
    list-style: none;
  }
  
  /* Container */
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  /* Buttons */
  .btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 0.9rem;
  }
  
  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-white);
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-dark);
    color: var(--color-white);
  }
  
  .btn-outline {
    background-color: transparent;
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
  }
  
  .btn-outline:hover {
    background-color: var(--color-bg-light-alt);
    color: var(--color-primary);
  }
  
  .btn-light {
    background-color: var(--color-white);
    color: var(--color-secondary);
  }
  
  .btn-light:hover {
    background-color: var(--color-bg-light);
    color: var(--color-secondary);
  }
  
  .btn-outline-light {
    background-color: transparent;
    border: 2px solid var(--color-white);
    color: var(--color-white);
  }
  
  .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
  }
  
  .btn-block {
    display: block;
    width: 100%;
  }
  
  /* Header */
  .header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: var(--color-white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
  }
  
  .logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-secondary);
  }
  
  .main-nav {
    display: none;
  }
  
  .nav-list {
    display: flex;
    gap: 1.5rem;
  }
  
  .nav-list a {
    color: var(--color-secondary);
    font-weight: 500;
    position: relative;
  }
  
  .nav-list a:hover,
  .nav-list a.active {
    color: var(--color-primary);
  }
  
  .nav-list a.active::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-primary);
  }
  
  .mobile-hidden {
    display: none;
  }
  
  .menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    background: transparent;
    border: none;
    cursor: pointer;
  }
  
  .bar {
    width: 100%;
    height: 3px;
    background-color: var(--color-secondary);
    transition: var(--transition);
  }
  
  .mobile-menu {
    display: none;
    padding: 1rem;
    background-color: var(--color-white);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .mobile-menu.active {
    display: block;
  }
  
  .mobile-nav-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .mobile-nav-list a {
    color: var(--color-secondary);
    font-weight: 500;
  }
  
  .mobile-nav-list a:hover,
  .mobile-nav-list a.active {
    color: var(--color-primary);
  }
  
  .mobile-nav-list button {
    margin-top: 0.5rem;
  }
  
  /* Hero Section */
  .hero {
    background: linear-gradient(to right, var(--color-bg-light), var(--color-bg-light-alt));
    padding: 4rem 0;
  }
  
  .hero-content {
    display: grid;
    gap: 2rem;
  }
  
  .hero-text {
    text-align: center;
  }
  
  .hero-text h1 {
    margin-bottom: 1rem;
  }
  
  .hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }
  
  .hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 300px;
    margin: 0 auto;
  }
  
  .hero-image {
    display: flex;
    justify-content: center;
  }
  
  .hero-image img {
    border-radius: var(--border-radius);
    object-fit: contain; /* Changed from cover to contain */
    width: 100%;
    max-height: 400px; /* Increased from 350px to 400px */
    box-shadow: var(--box-shadow);
  }
  
  /* Section Headers */
  .section-header {
    text-align: center;
    margin-bottom: 3rem;
  }
  
  .section-header h2 {
    margin-bottom: 0.5rem;
  }
  
  .section-title {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  /* Highlights Section */
  .highlights {
    padding: 4rem 0;
    background-color: var(--color-white);
  }
  
  .highlights-cards {
    display: grid;
    gap: 2rem;
  }
  
  .highlight-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background-color: var(--color-bg-light);
    border-radius: var(--border-radius);
  }
  
  .icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
  }
  
  .icon-circle.small {
    width: 40px;
    height: 40px;
  }
  
  .icon-circle i {
    color: var(--color-white);
    font-size: 1.5rem;
  }
  
  .icon-circle.small i {
    font-size: 1rem;
  }
  
  /* Impact Section */
  .impact {
    padding: 4rem 0;
    background-color: var(--color-bg-light-alt);
  }
  
  .stats-container {
    display: grid;
    gap: 2rem;
  }
  
  .stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background-color: var(--color-white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
  }
  
  .stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-secondary);
  }
  
  /* About Section */
  .about-section {
    padding: 4rem 0;
    background-color: var(--color-white);
  }
  
  .carousel-section {
    padding: 3rem 0;
    background-color: var(--color-bg-light);
    margin: 3rem 0;
    border-radius: var(--border-radius);
  }
  
  .values-section {
    padding: 2rem 0;
  }
  
  /* CEO Story Section */
  .ceo-story {
    padding: 3rem 0;
  }
  
  .story-container {
    display: grid;
    gap: 2rem;
    background-color: var(--color-bg-light);
    padding: 2rem;
    border-radius: var(--border-radius);
  }
  
  .ceo-image {
    display: flex;
    justify-content: center;
  }
  
  .ceo-image img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--color-white);
    box-shadow: var(--box-shadow);
  }
  
  .ceo-message h4 {
    margin-bottom: 1.5rem;
    color: var(--color-secondary);
  }
  
  .ceo-message p {
    margin-bottom: 1.5rem;
    font-style: italic;
  }
  
  .ceo-signature {
    margin-top: 2rem;
    text-align: right;
  }
  
  .signature-name {
    font-weight: 700;
    color: var(--color-secondary);
    margin-bottom: 0.25rem;
  }
  
  .signature-title {
    font-size: 0.9rem;
    color: var(--color-secondary-light);
  }
  
  /* Programs Section */
  .programs-section {
    padding: 4rem 0;
    background-color: var(--color-bg-light-alt);
  }
  
  /* Partners Section */
  .partners-section {
    padding: 4rem 0;
    background-color: var(--color-white);
  }
  
  .partner-benefits {
    padding: 4rem 0 2rem;
  }
  
  /* Contact Section */
  .contact-section {
    padding: 4rem 0;
    background-color: var(--color-bg-light);
  }
  
  .contact-categories {
    padding: 4rem 0 2rem;
  }
  
  /* Telegram Join Section */
  .telegram-join {
    display: grid;
    gap: 2rem;
    background-color: var(--color-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 3rem;
  }
  
  .telegram-icon {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .telegram-icon i {
    font-size: 5rem;
    color: #0088cc;
  }
  
  .telegram-content h3 {
    margin-bottom: 1rem;
    color: var(--color-secondary);
  }
  
  .telegram-highlight {
    background-color: rgba(209, 126, 126, 0.1);
    padding: 1rem;
    border-left: 4px solid var(--color-primary);
    margin: 1.5rem 0;
    font-weight: 500;
  }
  
  .telegram-btn {
    margin-top: 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .telegram-btn i {
    font-size: 1.2rem;
  }
  
  /* CTA Section */
  .cta {
    padding: 4rem 0;
    background-color: var(--color-secondary);
    color: var(--color-white);
    text-align: center;
  }
  
  .cta h2 {
    color: var(--color-white);
    margin-bottom: 1rem;
  }
  
  .cta p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .cta-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 300px;
    margin: 0 auto;
  }
  
  /* Footer */
  .footer {
    background-color: var(--color-secondary);
    color: var(--color-white);
    padding: 4rem 0 2rem;
  }
  
  .footer-content {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
  }
  
  .footer-column h3 {
    color: var(--color-white);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
  }
  
  .footer-column p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
  }
  
  .social-links {
    display: flex;
    gap: 1rem;
  }
  
  .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
    transition: var(--transition);
  }
  
  .social-links a:hover {
    background-color: var(--color-primary);
  }
  
  .footer-links li {
    margin-bottom: 0.75rem;
  }
  
  .footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition);
  }
  
  .footer-links a:hover {
    color: var(--color-white);
  }
  
  address p {
    margin-bottom: 0.5rem;
  }
  
  .footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0;
  }
  
  /* Two Column Layout */
  .two-column {
    display: grid;
    gap: 2rem;
  }
  
  .column {
    display: flex;
    flex-direction: column;
  }
  
  .two-column.reverse {
    direction: rtl;
  }
  
  .two-column.reverse .column {
    direction: ltr;
  }
  
  /* Mission & Vision Section */
  .tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--color-bg-light);
    color: var(--color-primary);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  /* Image Carousel - Completely Revised */
  .carousel-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
  }
  
  .carousel {
    position: relative;
    width: 100%;
    height: 350px;
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  .carousel-item {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    transition: transform 0.6s ease;
  }
  
  .carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .carousel-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 1rem;
  }
  
  .carousel-control {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--color-white);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    z-index: 10;
  }
  
  .carousel-control:hover {
    background-color: var(--color-primary);
    color: var(--color-white);
  }
  
  .carousel-indicators {
    display: flex;
    gap: 0.5rem;
  }
  
  .indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .indicator.active {
    background-color: var(--color-primary);
  }
  
  /* Values Grid */
  .values-grid {
    display: grid;
    gap: 2rem;
  }
  
  .value-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background-color: var(--color-bg-light);
    border-radius: var(--border-radius);
  }
  
  /* Partners Grid */
  .partners-logo-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .partner-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background-color: var(--color-white);
    border-radius: var(--border-radius);
    transition: var(--transition);
    filter: grayscale(100%);
    height: 120px;
  }
  
  .partner-logo:hover {
    filter: grayscale(0%);
  }
  
  .partner-logo img {
    max-height: 60px;
    max-width: 80%;
    object-fit: contain;
  }
  
  /* Benefits Grid */
  .benefits-grid {
    display: grid;
    gap: 2rem;
  }
  
  .benefit-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background-color: var(--color-white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  /* Tabs */
  .tabs-container {
    margin-top: 2rem;
  }
  
  .tabs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .tab-btn {
    padding: 0.75rem 1rem;
    background-color: var(--color-white);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .tab-btn.active {
    background-color: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
  }
  
  .tab-btn i {
    font-size: 1rem;
  }
  
  .tab-content {
    display: none;
  }
  
  .tab-content.active {
    display: block;
  }
  
  /* Feature List */
  .feature-list {
    margin: 1.5rem 0;
  }
  
  .feature-list li {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .feature-list i {
    color: var(--color-primary);
    margin-top: 0.25rem;
  }
  
  .feature-list h4 {
    margin-bottom: 0.25rem;
  }
  
  .feature-list p {
    margin-bottom: 0;
    font-size: 0.9rem;
  }
  
  .dot {
    width: 8px;
    height: 8px;
    background-color: var(--color-primary);
    border-radius: 50%;
    margin-top: 0.5rem;
  }
  
  /* Contact Info */
  .contact-info {
    margin-top: 2rem;
  }
  
  .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .contact-item h4 {
    margin-bottom: 0.25rem;
  }
  
  .contact-item p {
    margin-bottom: 0;
  }
  
  /* Contact Card */
  .contact-card {
    background-color: var(--color-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  .contact-method {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 1rem 0;
  }
  
  .contact-method i {
    color: var(--color-primary);
  }
  
  .contact-method p {
    margin-bottom: 0;
  }
  
  .download-link,
  .learn-more-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-weight: 600;
  }
  
  /* Media Queries */
  @media (min-width: 576px) {
    .hero-buttons,
    .cta-buttons {
      flex-direction: row;
      justify-content: center;
      max-width: none;
    }
  }
  
  @media (min-width: 768px) {
    h1 {
      font-size: 3rem;
    }
  
    h2 {
      font-size: 2.5rem;
    }
  
    .main-nav {
      display: block;
    }
  
    .mobile-hidden {
      display: block;
    }
  
    .menu-toggle {
      display: none;
    }
  
    .hero-content {
      grid-template-columns: 1fr 1fr;
      align-items: center;
    }
  
    .hero-text {
      text-align: left;
    }
  
    .hero-buttons {
      justify-content: flex-start;
    }
  
    .highlights-cards {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .stats-container {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .footer-content {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .two-column {
      grid-template-columns: 1fr 1fr;
      align-items: center;
    }
  
    .values-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .tabs {
      flex-direction: row;
      justify-content: center;
    }
  
    .tab-btn {
      flex: 1;
      justify-content: center;
    }
  
    .partners-logo-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  
    .benefits-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .story-container {
      grid-template-columns: 1fr 2fr;
      align-items: center;
    }
  
    .telegram-join {
      grid-template-columns: 1fr 3fr;
      align-items: center;
    }
  }
  
  @media (min-width: 992px) {
    h1 {
      font-size: 3.5rem;
    }
  
    .values-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .footer-content {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  /* Program Section Images */
  .two-column .column img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  /* Responsive adjustments for images */
  @media (max-width: 767px) {
    .hero-image img {
      max-height: 300px; /* Increased from 250px to 300px */
      object-fit: contain; /* Changed from cover to contain */
    }
  
    .carousel-item img,
    .two-column .column img {
      height: 250px;
    }
  
    .ceo-image img {
      width: 150px;
      height: 150px;
    }
  
    .partner-logo {
      height: 100px;
    }
  }
  